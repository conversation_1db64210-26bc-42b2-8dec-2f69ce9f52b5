<?php

namespace App\Services;

use App\Models\Field;
use App\Models\Reservation;
use Carbon\Carbon;

class FieldAvailabilityService
{
    /**
     * Check if a field is available for the given time slot
     */
    public function isFieldAvailable(Field $field, string $date, string $startTime, string $endTime, ?int $excludeReservationId = null): bool
    {
        // Check if time is within working hours
        if (! $this->isWithinWorkingHours($field, $startTime, $endTime)) {
            return false;
        }

        // Check for conflicting reservations
        return ! $this->hasConflictingReservations($field, $date, $startTime, $endTime, $excludeReservationId);
    }

    /**
     * Check if the time slot is within field working hours
     */
    public function isWithinWorkingHours(Field $field, string $startTime, string $endTime): bool
    {
        $openingTime = Carbon::createFromFormat('H:i', $field->opening_time);
        $closingTime = Carbon::createFromFormat('H:i', $field->closing_time);
        $start = Carbon::createFromFormat('H:i', $startTime);
        $end = Carbon::createFromFormat('H:i', $endTime);

        return $start->greaterThanOrEqualTo($openingTime) &&
               $end->lessThanOrEqualTo($closingTime);
    }

    /**
     * Check for conflicting reservations
     */
    public function hasConflictingReservations(Field $field, string $date, string $startTime, string $endTime, ?int $excludeReservationId = null): bool
    {
        // Input validation
        $this->validateTimeInputs($startTime, $endTime);

        // Use simplified overlap logic: two intervals [a1,a2] and [b1,b2] overlap if a1 < b2 AND b1 < a2
        // In our case: existing reservation overlaps with new reservation if:
        // existing.start_time < new.end_time AND existing.end_time > new.start_time
        $query = Reservation::where('field_id', $field->id)
            ->whereDate('booking_date', $date)      // Use whereDate for proper date comparison
            ->where('status', '!=', 'Cancelled')
            ->where('start_time', '<', $endTime)    // Existing starts before new ends
            ->where('end_time', '>', $startTime);   // Existing ends after new starts

        if ($excludeReservationId) {
            $query->where('id', '!=', $excludeReservationId);
        }

        return $query->exists();
    }

    /**
     * Validate time input parameters
     */
    private function validateTimeInputs(string $startTime, string $endTime): void
    {
        // Validate time format (H:MM or HH:MM) - allow single digit hours
        if (! preg_match('/^([0-9]|[01][0-9]|2[0-3]):[0-5][0-9]$/', $startTime)) {
            throw new \InvalidArgumentException("Invalid start time format: {$startTime}. Expected format: H:MM or HH:MM");
        }

        if (! preg_match('/^([0-9]|[01][0-9]|2[0-3]):[0-5][0-9]$/', $endTime)) {
            throw new \InvalidArgumentException("Invalid end time format: {$endTime}. Expected format: H:MM or HH:MM");
        }

        // Validate that end time is after start time using proper time comparison
        try {
            $startCarbon = Carbon::createFromFormat('H:i', $startTime);
            $endCarbon = Carbon::createFromFormat('H:i', $endTime);

            if ($startCarbon->greaterThanOrEqualTo($endCarbon)) {
                throw new \InvalidArgumentException("End time ({$endTime}) must be after start time ({$startTime})");
            }
        } catch (\Carbon\Exceptions\InvalidFormatException $e) {
            throw new \InvalidArgumentException("Invalid time format provided: {$e->getMessage()}");
        }
    }

    /**
     * Get available time slots for a field on a specific date
     */
    public function getAvailableTimeSlots(Field $field, string $date, int $durationHours = 1, ?int $excludeReservationId = null): array
    {
        $slots = [];
        $openingHour = (int) substr($field->opening_time, 0, 2);
        $closingHour = (int) substr($field->closing_time, 0, 2);

        // Generate all possible time slots
        for ($hour = $openingHour; $hour <= $closingHour - $durationHours; $hour++) {
            $startTime = sprintf('%02d:00', $hour);
            $endTime = sprintf('%02d:00', $hour + $durationHours);

            if ($this->isFieldAvailable($field, $date, $startTime, $endTime, $excludeReservationId)) {
                $slots[] = [
                    'start_time' => $startTime,
                    'end_time' => $endTime,
                    'display' => date('g:i A', strtotime($startTime)).' - '.date('g:i A', strtotime($endTime)),
                    'value' => $startTime,
                ];
            }
        }

        return $slots;
    }

    /**
     * Get field availability for multiple days
     */
    public function getFieldAvailabilityCalendar(Field $field, Carbon $startDate, Carbon $endDate): array
    {
        $calendar = [];
        $current = $startDate->copy();

        while ($current->lessThanOrEqualTo($endDate)) {
            $dateStr = $current->format('Y-m-d');
            $availableSlots = $this->getAvailableTimeSlots($field, $dateStr);

            $calendar[$dateStr] = [
                'date' => $current->format('Y-m-d'),
                'day_name' => $current->format('l'),
                'formatted_date' => $current->format('M d, Y'),
                'available_slots' => count($availableSlots),
                'slots' => $availableSlots,
                'is_past' => $current->isPast(),
                'is_today' => $current->isToday(),
            ];

            $current->addDay();
        }

        return $calendar;
    }

    /**
     * Get all fields availability for a specific date
     */
    public function getAllFieldsAvailability(string $date, int $durationHours = 1): array
    {
        $fields = Field::where('status', 'Active')->orderBy('name')->get();
        $availability = [];

        foreach ($fields as $field) {
            $availableSlots = $this->getAvailableTimeSlots($field, $date, $durationHours);

            $availability[] = [
                'field' => $field,
                'available_slots' => $availableSlots,
                'total_slots' => count($availableSlots),
                'is_available' => count($availableSlots) > 0,
            ];
        }

        return $availability;
    }

    /**
     * Validate reservation duration for a field
     */
    public function isValidDuration(Field $field, int $durationHours): bool
    {
        return $durationHours >= $field->min_booking_hours &&
               $durationHours <= $field->max_booking_hours;
    }

    /**
     * Get next available slot for a field
     */
    public function getNextAvailableSlot(Field $field, int $durationHours = 1, ?Carbon $fromDate = null): ?array
    {
        $fromDate = $fromDate ?: now();
        $maxDaysToCheck = 30; // Check up to 30 days ahead

        for ($i = 0; $i < $maxDaysToCheck; $i++) {
            $checkDate = $fromDate->copy()->addDays($i);
            $dateStr = $checkDate->format('Y-m-d');

            // Skip past dates
            if ($checkDate->isPast()) {
                continue;
            }

            $availableSlots = $this->getAvailableTimeSlots($field, $dateStr, $durationHours);

            if (! empty($availableSlots)) {
                return [
                    'date' => $dateStr,
                    'formatted_date' => $checkDate->format('M d, Y'),
                    'slot' => $availableSlots[0], // Return first available slot
                ];
            }
        }

        return null; // No available slots found
    }

    /**
     * Check if a reservation can be made (comprehensive validation)
     */
    public function canMakeReservation(Field $field, string $date, string $startTime, int $durationHours, ?int $excludeReservationId = null): array
    {
        $errors = [];

        // Check if date is in the past
        $reservationDate = Carbon::createFromFormat('Y-m-d', $date);
        if ($reservationDate->isPast()) {
            $errors[] = 'Cannot make reservations for past dates.';
        }

        // Check if field is active
        if ($field->status !== 'Active') {
            $errors[] = 'This field is currently not available for reservations.';
        }

        // Check duration validity
        if (! $this->isValidDuration($field, $durationHours)) {
            $errors[] = "Duration must be between {$field->min_booking_hours} and {$field->max_booking_hours} hours for this field.";
        }

        // Calculate end time
        $startCarbon = Carbon::createFromFormat('H:i', $startTime);
        $endTime = $startCarbon->copy()->addHours((int) $durationHours)->format('H:i');

        // Check working hours
        if (! $this->isWithinWorkingHours($field, $startTime, $endTime)) {
            $errors[] = "Reservation must be within field working hours ({$field->opening_time} - {$field->closing_time}).";
        }

        // Check availability
        if (! $this->isFieldAvailable($field, $date, $startTime, $endTime, $excludeReservationId)) {
            $errors[] = 'The selected time slot is not available.';
        }

        return [
            'can_reserve' => empty($errors),
            'errors' => $errors,
        ];
    }

    /**
     * Get reservation statistics for a field
     */
    public function getFieldStatistics(Field $field, ?Carbon $fromDate = null, ?Carbon $toDate = null): array
    {
        $fromDate = $fromDate ?: now()->startOfMonth();
        $toDate = $toDate ?: now()->endOfMonth();

        $reservations = Reservation::where('field_id', $field->id)
            ->whereBetween('booking_date', [$fromDate->format('Y-m-d'), $toDate->format('Y-m-d')])
            ->get();

        $totalReservations = $reservations->count();
        $confirmedReservations = $reservations->where('status', 'Confirmed')->count();
        $cancelledReservations = $reservations->where('status', 'Cancelled')->count();
        $totalRevenue = $reservations->where('status', '!=', 'Cancelled')->sum('total_cost');
        $totalHours = $reservations->where('status', '!=', 'Cancelled')->sum('duration_hours');

        return [
            'total_reservations' => $totalReservations,
            'confirmed_reservations' => $confirmedReservations,
            'cancelled_reservations' => $cancelledReservations,
            'cancellation_rate' => $totalReservations > 0 ? round(($cancelledReservations / $totalReservations) * 100, 2) : 0,
            'total_revenue' => $totalRevenue,
            'total_hours' => $totalHours,
            'average_reservation_value' => $confirmedReservations > 0 ? round($totalRevenue / $confirmedReservations, 2) : 0,
        ];
    }
}
