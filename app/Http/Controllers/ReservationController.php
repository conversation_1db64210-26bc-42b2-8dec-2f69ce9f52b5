<?php

namespace App\Http\Controllers;

use App\Models\Field;
use App\Models\Reservation;
use App\Models\Utility;
use App\Rules\ActiveUtilityRule;
use App\Services\FieldAvailabilityService;
use App\Services\ReservationCostService;
use App\Services\ReservationValidationService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ReservationController extends Controller
{
    protected FieldAvailabilityService $availabilityService;

    protected ReservationCostService $costService;

    protected ReservationValidationService $validationService;

    public function __construct(
        FieldAvailabilityService $availabilityService,
        ReservationCostService $costService,
        ReservationValidationService $validationService
    ) {
        $this->availabilityService = $availabilityService;
        $this->costService = $costService;
        $this->validationService = $validationService;
    }

    /**
     * Display a listing of the user's reservations.
     */
    public function index(Request $request)
    {
        $user = auth()->user();

        // Get user's reservations with field information
        $reservations = Reservation::with('field')
            ->forUser($user->id)
            ->orderBy('booking_date', 'desc')
            ->orderBy('start_time', 'desc')
            ->paginate(10);

        // Get upcoming reservations count for dashboard
        $upcomingCount = Reservation::forUser($user->id)
            ->upcoming()
            ->active()
            ->count();

        return view('reservations.index', compact('reservations', 'upcomingCount'));
    }

    /**
     * Show the form for creating a new reservation.
     */
    public function create(Request $request)
    {
        // Get available fields
        $fields = Field::where('status', 'Active')->orderBy('name')->get();

        // Get active utilities
        $utilities = Utility::where('is_active', true)->orderBy('name')->get();

        // Get selected field if provided
        $selectedField = null;
        if ($request->has('field_id')) {
            $selectedField = Field::find($request->field_id);
        }

        // Get selected date if provided (handle both 'date' and 'booking_date' parameters)
        $selectedDate = $request->get('date') ?: $request->get('booking_date', now()->addDay()->format('Y-m-d'));

        // Get selected time if provided
        $selectedTime = $request->get('time') ?: $request->get('start_time');

        // Get selected duration if provided
        $selectedDuration = $request->get('duration_hours', 1);

        // Get field availability for the next 7 days if a field is selected
        $fieldAvailability = [];
        if ($selectedField) {
            $startDate = Carbon::parse($selectedDate);
            $endDate = $startDate->copy()->addDays(6);
            $fieldAvailability = $this->availabilityService->getFieldAvailabilityCalendar($selectedField, $startDate, $endDate);
        }

        return view('reservations.create', compact('fields', 'utilities', 'selectedField', 'selectedDate', 'selectedTime', 'selectedDuration', 'fieldAvailability'));
    }

    /**
     * Store a newly created reservation in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'field_id' => ['required', 'exists:fields,id'],
            'booking_date' => ['required', 'date', 'after_or_equal:today'],
            'start_time' => ['required', 'date_format:H:i'],
            'duration_hours' => ['required', 'integer', 'min:1'],
            'customer_name' => ['nullable', 'string', 'max:255'],
            'customer_email' => ['nullable', 'email', 'max:255'],
            'customer_phone' => ['nullable', 'string', 'max:20'],
            'special_requests' => ['nullable', 'string', 'max:1000'],
            'utilities' => ['nullable', 'array'],
            'utilities.*.id' => ['required_with:utilities', new ActiveUtilityRule],
            'utilities.*.hours' => ['required_with:utilities', 'integer', 'min:1'],
        ]);

        if ($validator->fails()) {
            if ($request->expectsJson()) {
                return response()->json(['errors' => $validator->errors()], 422);
            }

            return back()->withErrors($validator)->withInput();
        }

        $field = Field::findOrFail($request->field_id);

        // Calculate end time
        $startTime = Carbon::createFromFormat('H:i', $request->start_time);
        $endTime = $startTime->copy()->addHours((int) $request->duration_hours);

        // Validate working hours
        if (! $field->isWithinWorkingHours($request->start_time, $endTime->format('H:i'))) {
            $error = [
                'start_time' => 'Reservation must be within field working hours ('.
                               $field->opening_time.' - '.$field->closing_time.')',
            ];
            if ($request->expectsJson()) {
                return response()->json(['errors' => $error], 422);
            }

            return back()->withErrors($error)->withInput();
        }

        // Validate duration
        if (! $field->isValidDuration($request->duration_hours)) {
            $error = [
                'duration_hours' => 'Duration must be between '.$field->min_booking_hours.
                                   ' and '.$field->max_booking_hours.' hours for this field.',
            ];
            if ($request->expectsJson()) {
                return response()->json(['errors' => $error], 422);
            }

            return back()->withErrors($error)->withInput();
        }

        // Check availability
        if (! $field->isAvailableAt($request->booking_date, $request->start_time, $endTime->format('H:i'))) {
            $error = [
                'start_time' => 'The selected time slot is not available. Please choose a different time.',
            ];
            if ($request->expectsJson()) {
                return response()->json(['errors' => $error], 422);
            }

            return back()->withErrors($error)->withInput();
        }

        // Calculate total cost using server-side authoritative calculation
        $utilities = $request->utilities ?? [];
        $costCalculation = $this->costService->calculateTotalCostWithUtilities(
            $field,
            $request->duration_hours,
            $request->start_time,
            $utilities
        );

        $totalCost = $costCalculation['total_cost'];
        $utilityData = $costCalculation['utility_breakdown'];

        // Create reservation
        $reservation = Reservation::create([
            'field_id' => $request->field_id,
            'user_id' => auth()->id(),
            'booking_date' => $request->booking_date,
            'start_time' => $request->start_time,
            'end_time' => $endTime->format('H:i'),
            'duration_hours' => $request->duration_hours,
            'total_cost' => $totalCost,
            'status' => 'Pending',
            'customer_name' => $request->customer_name ?: auth()->user()->name,
            'customer_email' => $request->customer_email ?: auth()->user()->email,
            'customer_phone' => $request->customer_phone,
            'special_requests' => $request->special_requests,
        ]);

        // /////////////////////////////////////////////////////////////////////////////////////////////////////
        // Save utilities (assuming pivot table `reservation_utility`)
        foreach ($utilityData as $data) {
            $reservation->utilities()->attach($data['utility_id'], [
                'hours' => $data['hours'],
                'rate' => $data['rate'],
                'cost' => $data['cost'],
            ]);
        }
        // /////////////////////////////////////////////////////////////////////////////////////////////////////

        // Auto-confirm for Phase 1 (no approval workflow)
        $reservation->autoConfirm();

        return redirect()->route('reservations.show', $reservation)
            ->with('success', 'Reservation created successfully! Your reservation has been confirmed.');
    }

    /**
     * Display the specified reservation.
     */
    public function show(Reservation $reservation)
    {
        // Authorization check
        if ($reservation->user_id !== auth()->id()) {
            abort(403, 'You can only view your own reservations.');
        }

        $reservation->load('field');

        return view('reservations.show', compact('reservation'));
    }

    /**
     * Show the form for editing the specified reservation.
     */
    public function edit(Reservation $reservation)
    {
        // Authorization check
        if ($reservation->user_id !== auth()->id()) {
            abort(403, 'You can only edit your own reservations.');
        }

        // Check if reservation can be modified
        if (! $reservation->canBeModified()) {
            return back()->with('error', 'This reservation cannot be modified. Reservations can only be changed up to 24 hours before the scheduled time.');
        }
        // ////////////////////////////////////////////////////////
        // $fields = Field::all();
        $utilities = Utility::all();

        $reservationUtilities = $reservation->utilities->map(function ($utility) {
            return [
                'id' => $utility->id,
                'name' => $utility->name,
                'rate' => $utility->pivot->rate,
                'hours' => $utility->pivot->hours,
                'cost' => $utility->pivot->cost,
            ];
        });
        // ////////////////////////////////////////////////////////
        $fields = Field::where('status', 'Active')->orderBy('name')->get();

        return view('reservations.edit', compact('reservation', 'fields', 'utilities', 'reservationUtilities'));
    }

    /**
     * Update the specified reservation in storage.
     */
    public function update(Request $request, Reservation $reservation)
    {
        // Authorization check
        if ($reservation->user_id !== auth()->id()) {
            abort(403, 'You can only edit your own reservations.');
        }

        // Check if reservation can be modified
        if (! $reservation->canBeModified()) {
            return back()->with('error', 'This reservation cannot be modified. Reservations can only be changed up to 24 hours before the scheduled time.');
        }

        $validator = Validator::make($request->all(), [
            'field_id' => ['required', 'exists:fields,id'],
            'booking_date' => ['required', 'date', 'after_or_equal:today'],
            'start_time' => ['required', 'date_format:H:i'],
            'duration_hours' => ['required', 'integer', 'min:1'],
            'customer_name' => ['nullable', 'string', 'max:255'],
            'customer_email' => ['nullable', 'email', 'max:255'],
            'customer_phone' => ['nullable', 'string', 'max:20'],
            'special_requests' => ['nullable', 'string', 'max:1000'],
            'utilities' => ['nullable', 'array'],
            'utilities.*.id' => ['required_with:utilities', new ActiveUtilityRule],
            'utilities.*.hours' => ['required_with:utilities', 'integer', 'min:1'],
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $field = Field::findOrFail($request->field_id);

        // Calculate end time
        $startTime = Carbon::createFromFormat('H:i', $request->start_time);
        $endTime = $startTime->copy()->addHours((int) $request->duration_hours);

        // Validate working hours
        if (! $field->isWithinWorkingHours($request->start_time, $endTime->format('H:i'))) {
            return back()->withErrors([
                'start_time' => 'Reservation must be within field working hours ('.
                               $field->opening_time.' - '.$field->closing_time.')',
            ])->withInput();
        }

        // Validate duration
        if (! $field->isValidDuration($request->duration_hours)) {
            return back()->withErrors([
                'duration_hours' => 'Duration must be between '.$field->min_booking_hours.
                                   ' and '.$field->max_booking_hours.' hours for this field.',
            ])->withInput();
        }

        // Check availability (excluding current reservation)
        if (! $field->isAvailableAt($request->booking_date, $request->start_time, $endTime->format('H:i'), $reservation->id)) {
            return back()->withErrors([
                'start_time' => 'The selected time slot is not available. Please choose a different time.',
            ])->withInput();
        }

        // Calculate total cost using server-side authoritative calculation (consistent with store method)
        $utilities = $request->utilities ?? [];
        $costCalculation = $this->costService->calculateTotalCostWithUtilities(
            $field,
            $request->duration_hours,
            $request->start_time,
            $utilities
        );

        $totalCost = $costCalculation['total_cost'];
        $utilityData = $costCalculation['utility_breakdown'];

        // Update reservation
        $reservation->update([
            'field_id' => $request->field_id,
            'booking_date' => $request->booking_date,
            'start_time' => $request->start_time,
            'end_time' => $endTime->format('H:i'),
            'duration_hours' => $request->duration_hours,
            'total_cost' => $totalCost,
            'customer_name' => $request->customer_name ?: auth()->user()->name,
            'customer_email' => $request->customer_email ?: auth()->user()->email,
            'customer_phone' => $request->customer_phone,
            'special_requests' => $request->special_requests,
        ]);

        // Update utilities using server-calculated data
        $reservation->utilities()->detach(); // Clear old utilities

        foreach ($utilityData as $utility) {
            $reservation->utilities()->attach($utility['utility_id'], [
                'hours' => $utility['hours'],
                'rate' => $utility['rate'],
                'cost' => $utility['cost'],
            ]);
        }

        return redirect()->route('reservations.show', $reservation)
            ->with('success', 'Reservation updated successfully!');
    }

    /**
     * Cancel the specified reservation.
     */
    public function cancel(Reservation $reservation)
    {
        // Authorization check
        if ($reservation->user_id !== auth()->id()) {
            abort(403, 'You can only cancel your own reservations.');
        }

        if (! $reservation->canBeCancelled()) {
            return back()->with('error', 'This reservation cannot be cancelled. Reservations can only be cancelled up to 24 hours before the scheduled time.');
        }

        $reservation->cancel();

        return back()->with('success', 'Reservation cancelled successfully.');
    }

    /**
     * Get field availability for AJAX requests
     */
    public function checkAvailability(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'field_id' => ['required', 'exists:fields,id'],
            'date' => ['required', 'date'],
            'duration_hours' => ['required', 'integer', 'min:1', 'max:8'],
            'exclude_reservation_id' => ['nullable', 'integer'],
        ]);

        if ($validator->fails()) {
            return response()->json(['available' => false, 'message' => 'Invalid input', 'slots' => []]);
        }

        $field = Field::findOrFail($request->field_id);
        $durationHours = (int) $request->duration_hours;
        $excludeReservationId = $request->exclude_reservation_id;

        // Get available time slots for the duration (excluding specified reservation if provided)
        $slots = $this->availabilityService->getAvailableTimeSlots($field, $request->date, $durationHours, $excludeReservationId);

        return response()->json([
            'available' => count($slots) > 0,
            'message' => count($slots) > 0 ? count($slots).' time slots available' : 'No available time slots',
            'slots' => $slots,
        ]);
    }

    /**
     * Get cost estimate for AJAX requests
     */
    public function getCostEstimate(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'field_id' => ['required', 'exists:fields,id'],
            'duration_hours' => ['required', 'integer', 'min:1', 'max:8'],
            'start_time' => ['nullable', 'date_format:H:i'],
            'utilities' => ['nullable', 'array'],
            'utilities.*.id' => ['required_with:utilities', 'exists:utilities,id'],
            'utilities.*.hours' => ['required_with:utilities', 'integer', 'min:1'],
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => 'Invalid input']);
        }

        $estimate = $this->costService->getReservationEstimate(
            (int) $request->field_id,
            (int) $request->duration_hours,
            $request->start_time,
            $request->utilities ?? []
        );

        return response()->json($estimate);
    }
}
